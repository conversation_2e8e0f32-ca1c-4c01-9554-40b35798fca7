<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    public function up()
    {
        Schema::create('event_speakers', function (Blueprint $table) {
            $table->id();
            $table->foreignId('event_id')->constrained()->onDelete('cascade');
            $table->foreignId('speaker_id')->constrained()->onDelete('cascade');
            $table->enum('role', ['presenter', 'moderator', 'panelist'])->default('presenter');
            $table->integer('display_order')->default(0);
            $table->timestamps();
            
            $table->unique(['event_id', 'speaker_id']);
            $table->index(['event_id', 'role']);
        });
    }

    public function down()
    {
        Schema::dropIfExists('event_speakers');
    }
};
