<?php

namespace Database\Seeders;

use App\Models\Speaker;
use Illuminate\Database\Seeder;

class Speaker<PERSON><PERSON><PERSON> extends Seeder
{
    public function run()
    {
        $speakers = [
            // Conference Leadership
            [
                'name' => '<PERSON>',
                'title' => 'Prof.',
                'position' => 'Conference Chair',
                'organization' => 'College of Engineering Technology Janzour',
                'bio' => 'Professor <PERSON> serves as the Conference Chair for IREGO Conference.',
                'country' => 'Libya',
                'city' => 'Tripoli',
                'type' => 'committee_chair',
                'committee_type' => 'organizing',
                'is_featured' => true,
                'display_order' => 1,
            ],
            [
                'name' => 'Mahboub Edreder',
                'title' => 'Dr.',
                'position' => 'Conference Co-Chair',
                'organization' => 'National Oil Corporation',
                'bio' => 'Dr. <PERSON><PERSON> serves as the Conference Co-Chair for IREGO Conference.',
                'country' => 'Libya',
                'city' => 'Tripoli',
                'type' => 'committee_chair',
                'committee_type' => 'organizing',
                'is_featured' => true,
                'display_order' => 2,
            ],
            
            // Scientific Committee Chair
            [
                'name' => '<PERSON>',
                'title' => 'Dr.',
                'position' => 'Scientific Committee Chair',
                'organization' => 'College of Engineering Technology',
                'bio' => 'Dr. Mohamed Al-<PERSON>hami leads the Scientific Committee for IREGO Conference.',
                'country' => 'Libya',
                'city' => 'Tripoli',
                'type' => 'committee_chair',
                'committee_type' => 'scientific',
                'is_featured' => true,
                'display_order' => 3,
            ],
            
            // Organizing Committee Chair
            [
                'name' => 'Muad Muftah Al-Jarmi',
                'title' => 'Eng.',
                'position' => 'Organizing Committee Chair',
                'organization' => 'Zawia Oil Refining Company',
                'bio' => 'Engineer Muad Muftah Al-Jarmi leads the Organizing Committee for IREGO Conference.',
                'country' => 'Libya',
                'city' => 'Zawia',
                'type' => 'committee_chair',
                'committee_type' => 'organizing',
                'is_featured' => true,
                'display_order' => 4,
            ],
            
            // Advisory Committee Members
            [
                'name' => 'Nouri Al-Fallo',
                'title' => 'Dr.',
                'position' => 'Advisory Committee Member',
                'organization' => 'Nafusa Oil Operations Company',
                'country' => 'Libya',
                'city' => 'Tripoli',
                'type' => 'committee_member',
                'committee_type' => 'advisory',
                'display_order' => 5,
            ],
            [
                'name' => 'Mohamed Al-jemni',
                'title' => 'Prof. Dr.',
                'position' => 'Advisory Committee Member (IEEE Senior Member)',
                'organization' => 'IEEE',
                'country' => 'Libya',
                'type' => 'committee_member',
                'committee_type' => 'advisory',
                'display_order' => 6,
            ],
            [
                'name' => 'Habib Kammoun',
                'title' => 'Prof. Dr.',
                'position' => 'Advisory Committee Member (IEEE Senior Member)',
                'organization' => 'IEEE',
                'country' => 'Tunisia',
                'type' => 'committee_member',
                'committee_type' => 'advisory',
                'display_order' => 7,
            ],
        ];

        foreach ($speakers as $speakerData) {
            Speaker::create(array_merge([
                'conference_id' => 1,
                'is_active' => true,
            ], $speakerData));
        }
    }
}
