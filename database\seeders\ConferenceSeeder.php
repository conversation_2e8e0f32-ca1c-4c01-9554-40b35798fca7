<?php

namespace Database\Seeders;

use App\Models\Conference;
use Illuminate\Database\Seeder;

class ConferenceSeeder extends Seeder
{
    public function run()
    {
        Conference::create([
            'title' => 'International Renewable Energy, Gas & Oil, and Climate Change Conference',
            'subtitle' => 'IREGO Conference 2025',
            'description' => 'The Renewable Energy, Gas & Oil, and Climate Change Conference will take place in Tripoli, Libya, in November 2025, bringing together experts, researchers, industry leaders, and policymakers to address pressing energy and environmental challenges. This pioneering event aims to bridge the gap between academia, industry, and government by fostering dialogue and collaboration on innovative solutions in renewable energy, oil and gas, and climate change mitigation.',
            'start_date' => '2025-11-25 09:00:00',
            'end_date' => '2025-11-27 17:00:00',
            'location' => 'Tripoli, Libya',
            'venue' => 'Conference Center Tripoli',
            'website_url' => 'https://irego-conference.ly',
            'email' => '<EMAIL>',
            'phone' => '+218-21-123-4567',
            'registration_fee_local' => 200.00,
            'registration_fee_international' => 200.00,
            'currency_local' => 'LYD',
            'currency_international' => 'EUR',
            'abstract_deadline' => '2025-06-15 23:59:59',
            'notification_date' => '2025-08-15 23:59:59',
            'final_paper_deadline' => '2025-10-01 23:59:59',
            'is_active' => true,
            'meta_title' => 'IREGO Conference 2025 - International Renewable Energy Conference',
            'meta_description' => 'Join the International Renewable Energy, Gas & Oil, and Climate Change Conference in Tripoli, Libya, November 25-27, 2025.',
            'meta_keywords' => 'renewable energy, climate change, oil gas, conference, Libya, Tripoli, sustainability',
        ]);
    }
}
