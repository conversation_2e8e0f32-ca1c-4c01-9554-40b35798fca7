# IREGO Conference Management System

نظام إدارة مؤتمرات IREGO - نظام شامل لإدارة المؤتمرات الدولية مع لوحة تحكم أدمن متكاملة وRESTful API.

## المميزات

### 🎯 إدارة المؤتمرات
- إنشاء وإدارة المؤتمرات المتعددة
- معلومات شاملة للمؤتمر (التواريخ، المكان، الرسوم)
- رفع الصور والشعارات
- إدارة المواعيد المهمة

### 👥 إدارة المتحدثين
- إضافة وإدارة المتحدثين الرئيسيين
- إدارة اللجان (الاستشارية، العلمية، التنظيمية)
- معلومات شاملة للمتحدثين مع الصور
- ربط المتحدثين بالفعاليات

### 📝 إدارة التسجيلات
- تسجيل المشاركين (محلي/دولي)
- إدارة حالات الدفع والتأكيد
- تصدير البيانات (Excel/CSV)
- نظام Check-in للمشاركين
- رموز التأكيد الفريدة

### 🤝 إدارة الشركاء والرعاة
- تصنيف الرعاة (رسمي، ذهبي، فضي، برونزي)
- رفع شعارات الشركاء
- معلومات الاتصال والمواقع

### 📰 إدارة الأخبار
- نشر الأخبار والمقالات
- محرر نصوص غني
- إدارة الصور المميزة
- نظام النشر والجدولة

### 📅 إدارة الفعاليات
- جدولة الفعاليات والجلسات
- ربط المتحدثين بالفعاليات
- إدارة الأماكن والقاعات
- أنواع مختلفة من الفعاليات

### ⚙️ الإعدادات العامة
- إعدادات الموقع والاتصال
- إعدادات البريد الإلكتروني
- إعدادات وسائل التواصل الاجتماعي
- إعدادات المؤتمر

### 🔐 الأمان
- مصادقة JWT للـ API
- حماية ضد SQL Injection
- حماية CSRF
- صلاحيات الوصول للأدمن
- تشفير كلمات المرور

### 📊 لوحة التحكم
- إحصائيات شاملة
- رسوم بيانية تفاعلية
- التسجيلات الحديثة
- الفعاليات القادمة

## متطلبات النظام

- PHP 8.1 أو أحدث
- MySQL 5.7 أو أحدث
- Composer
- Node.js و npm (اختياري للتطوير)

## التثبيت

### 1. تحميل المشروع
```bash
git clone https://github.com/your-repo/irego-conference.git
cd irego-conference
```

### 2. تثبيت التبعيات
```bash
composer install
```

### 3. إعداد البيئة
```bash
cp .env.example .env
php artisan key:generate
```

### 4. تكوين قاعدة البيانات
قم بتحرير ملف `.env` وإعداد اتصال قاعدة البيانات:

```env
DB_CONNECTION=mysql
DB_HOST=127.0.0.1
DB_PORT=3306
DB_DATABASE=irego_conference
DB_USERNAME=your_username
DB_PASSWORD=your_password
```

### 5. إنشاء قاعدة البيانات
```bash
php artisan migrate
php artisan db:seed
```

### 6. إنشاء مفتاح JWT
```bash
php artisan jwt:secret
```

### 7. إنشاء رابط التخزين
```bash
php artisan storage:link
```

### 8. تشغيل الخادم
```bash
php artisan serve
```

## بيانات الدخول الافتراضية

### Super Admin
- البريد الإلكتروني: `<EMAIL>`
- كلمة المرور: `admin123`

### Admin
- البريد الإلكتروني: `<EMAIL>`
- كلمة المرور: `conference123`

## استخدام API

### المصادقة
```bash
POST /api/v1/auth/login
Content-Type: application/json

{
    "email": "<EMAIL>",
    "password": "admin123"
}
```

### الحصول على المؤتمرات
```bash
GET /api/v1/conferences
Authorization: Bearer {token}
```

### التسجيل في المؤتمر
```bash
POST /api/v1/registrations
Content-Type: application/json

{
    "conference_id": 1,
    "first_name": "أحمد",
    "last_name": "محمد",
    "email": "<EMAIL>",
    "country": "Libya",
    "participant_type": "local",
    "registration_type": "attendee"
}
```

## الروابط المهمة

- لوحة التحكم: `http://localhost:8000/admin`
- API Documentation: `http://localhost:8000/api/v1`
- تسجيل الدخول: `http://localhost:8000/admin/login`

## البنية

```
app/
├── Http/
│   ├── Controllers/
│   │   ├── Api/          # API Controllers
│   │   └── Admin/        # Admin Controllers
│   └── Middleware/       # Custom Middleware
├── Models/               # Eloquent Models
├── Exports/             # Excel Export Classes
└── Providers/           # Service Providers

database/
├── migrations/          # Database Migrations
└── seeders/            # Database Seeders

resources/
└── views/
    ├── admin/          # Admin Panel Views
    └── layouts/        # Layout Templates

routes/
├── api.php             # API Routes
└── web.php             # Web Routes
```

## API Endpoints

### Authentication
- `POST /api/v1/auth/login` - تسجيل الدخول
- `POST /api/v1/auth/logout` - تسجيل الخروج
- `GET /api/v1/auth/me` - معلومات المستخدم
- `POST /api/v1/auth/refresh` - تجديد الرمز المميز

### Conferences
- `GET /api/v1/conferences` - قائمة المؤتمرات
- `GET /api/v1/conferences/{id}` - تفاصيل المؤتمر
- `GET /api/v1/conferences/current` - المؤتمر الحالي

### Speakers
- `GET /api/v1/speakers` - قائمة المتحدثين
- `GET /api/v1/speakers/{id}` - تفاصيل المتحدث
- `GET /api/v1/speakers/committees/all` - اللجان
- `GET /api/v1/speakers/keynotes/all` - المتحدثين الرئيسيين

### Registrations
- `POST /api/v1/registrations` - تسجيل جديد
- `GET /api/v1/registrations/{code}` - تفاصيل التسجيل
- `POST /api/v1/registrations/check-status` - فحص حالة التسجيل

### News
- `GET /api/v1/news` - قائمة الأخبار
- `GET /api/v1/news/{slug}` - تفاصيل الخبر
- `GET /api/v1/news/featured` - الأخبار المميزة

### Sponsors
- `GET /api/v1/sponsors` - قائمة الشركاء
- `GET /api/v1/sponsors/{id}` - تفاصيل الشريك

### Events
- `GET /api/v1/events` - قائمة الفعاليات
- `GET /api/v1/events/{id}` - تفاصيل الفعالية
- `GET /api/v1/events/schedule/all` - جدول الفعاليات

## النشر على VPS

### 1. رفع الملفات
```bash
rsync -avz --exclude 'node_modules' --exclude '.git' ./ user@your-server:/var/www/irego-conference/
```

### 2. إعداد الخادم
```bash
# تثبيت التبعيات
composer install --optimize-autoloader --no-dev

# إعداد الصلاحيات
chmod -R 755 storage bootstrap/cache
chown -R www-data:www-data storage bootstrap/cache

# تشغيل الهجرات
php artisan migrate --force
php artisan db:seed --force

# تحسين الأداء
php artisan config:cache
php artisan route:cache
php artisan view:cache
```

### 3. إعداد Apache/Nginx
قم بتوجيه الدومين إلى مجلد `public/`

## الدعم والمساهمة

للحصول على الدعم أو المساهمة في المشروع، يرجى التواصل معنا.

## الترخيص

هذا المشروع مرخص تحت رخصة MIT.
