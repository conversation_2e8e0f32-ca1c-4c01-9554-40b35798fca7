<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use App\Models\Event;
use App\Models\Conference;
use App\Models\Speaker;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Validator;

class EventController extends Controller
{
    public function index(Request $request)
    {
        $query = Event::with(['conference', 'speakers']);

        // Filter by conference
        if ($request->has('conference_id') && $request->conference_id) {
            $query->where('conference_id', $request->conference_id);
        }

        // Filter by type
        if ($request->has('type') && $request->type) {
            $query->where('type', $request->type);
        }

        // Filter by date
        if ($request->has('date') && $request->date) {
            $query->whereDate('start_time', $request->date);
        }

        // Search
        if ($request->has('search') && $request->search) {
            $search = $request->search;
            $query->where(function($q) use ($search) {
                $q->where('title', 'like', "%{$search}%")
                  ->orWhere('description', 'like', "%{$search}%")
                  ->orWhere('location', 'like', "%{$search}%");
            });
        }

        $events = $query->orderBy('start_time')->paginate(15);
        $conferences = Conference::active()->orderBy('title')->get();

        return view('admin.events.index', compact('events', 'conferences'));
    }

    public function create()
    {
        $conferences = Conference::active()->orderBy('title')->get();
        $speakers = Speaker::active()->orderBy('name')->get();
        return view('admin.events.create', compact('conferences', 'speakers'));
    }

    public function store(Request $request)
    {
        $validator = Validator::make($request->all(), [
            'conference_id' => 'required|exists:conferences,id',
            'title' => 'required|string|max:255',
            'description' => 'nullable|string',
            'type' => 'required|in:session,keynote,workshop,break,lunch,registration',
            'start_time' => 'required|date',
            'end_time' => 'required|date|after:start_time',
            'location' => 'nullable|string|max:255',
            'room' => 'nullable|string|max:100',
            'max_participants' => 'nullable|integer|min:1',
            'is_parallel' => 'boolean',
            'display_order' => 'nullable|integer|min:0',
            'is_active' => 'boolean',
            'speakers' => 'nullable|array',
            'speakers.*' => 'exists:speakers,id',
        ]);

        if ($validator->fails()) {
            return back()->withErrors($validator)->withInput();
        }

        $data = $request->except('speakers');
        $data['is_parallel'] = $request->has('is_parallel');
        $data['is_active'] = $request->has('is_active');
        $data['display_order'] = $request->display_order ?: 0;
        $data['current_participants'] = 0;

        $event = Event::create($data);

        // Attach speakers
        if ($request->has('speakers') && is_array($request->speakers)) {
            $speakerData = [];
            foreach ($request->speakers as $index => $speakerId) {
                $speakerData[$speakerId] = [
                    'role' => 'presenter',
                    'display_order' => $index,
                ];
            }
            $event->speakers()->attach($speakerData);
        }

        return redirect()->route('admin.events.index')
            ->with('success', 'Event created successfully.');
    }

    public function show(Event $event)
    {
        $event->load(['conference', 'speakers']);
        return view('admin.events.show', compact('event'));
    }

    public function edit(Event $event)
    {
        $conferences = Conference::active()->orderBy('title')->get();
        $speakers = Speaker::active()->orderBy('name')->get();
        $event->load('speakers');
        return view('admin.events.edit', compact('event', 'conferences', 'speakers'));
    }

    public function update(Request $request, Event $event)
    {
        $validator = Validator::make($request->all(), [
            'conference_id' => 'required|exists:conferences,id',
            'title' => 'required|string|max:255',
            'description' => 'nullable|string',
            'type' => 'required|in:session,keynote,workshop,break,lunch,registration',
            'start_time' => 'required|date',
            'end_time' => 'required|date|after:start_time',
            'location' => 'nullable|string|max:255',
            'room' => 'nullable|string|max:100',
            'max_participants' => 'nullable|integer|min:1',
            'current_participants' => 'nullable|integer|min:0',
            'is_parallel' => 'boolean',
            'display_order' => 'nullable|integer|min:0',
            'is_active' => 'boolean',
            'speakers' => 'nullable|array',
            'speakers.*' => 'exists:speakers,id',
        ]);

        if ($validator->fails()) {
            return back()->withErrors($validator)->withInput();
        }

        $data = $request->except('speakers');
        $data['is_parallel'] = $request->has('is_parallel');
        $data['is_active'] = $request->has('is_active');
        $data['display_order'] = $request->display_order ?: 0;

        $event->update($data);

        // Sync speakers
        if ($request->has('speakers') && is_array($request->speakers)) {
            $speakerData = [];
            foreach ($request->speakers as $index => $speakerId) {
                $speakerData[$speakerId] = [
                    'role' => 'presenter',
                    'display_order' => $index,
                ];
            }
            $event->speakers()->sync($speakerData);
        } else {
            $event->speakers()->detach();
        }

        return redirect()->route('admin.events.index')
            ->with('success', 'Event updated successfully.');
    }

    public function destroy(Event $event)
    {
        $event->speakers()->detach();
        $event->delete();

        return redirect()->route('admin.events.index')
            ->with('success', 'Event deleted successfully.');
    }
}
