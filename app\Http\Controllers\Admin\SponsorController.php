<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use App\Models\Sponsor;
use App\Models\Conference;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Storage;
use Illuminate\Support\Facades\Validator;

class SponsorController extends Controller
{
    public function index(Request $request)
    {
        $query = Sponsor::with('conference');

        // Filter by conference
        if ($request->has('conference_id') && $request->conference_id) {
            $query->where('conference_id', $request->conference_id);
        }

        // Filter by type
        if ($request->has('type') && $request->type) {
            $query->where('type', $request->type);
        }

        // Search
        if ($request->has('search') && $request->search) {
            $search = $request->search;
            $query->where(function($q) use ($search) {
                $q->where('name', 'like', "%{$search}%")
                  ->orWhere('description', 'like', "%{$search}%");
            });
        }

        $sponsors = $query->orderBy('display_order')->orderBy('name')->paginate(15);
        $conferences = Conference::active()->orderBy('title')->get();

        return view('admin.sponsors.index', compact('sponsors', 'conferences'));
    }

    public function create()
    {
        $conferences = Conference::active()->orderBy('title')->get();
        return view('admin.sponsors.create', compact('conferences'));
    }

    public function store(Request $request)
    {
        $validator = Validator::make($request->all(), [
            'conference_id' => 'required|exists:conferences,id',
            'name' => 'required|string|max:255',
            'description' => 'nullable|string',
            'logo' => 'nullable|image|mimes:jpeg,png,jpg,gif,svg|max:2048',
            'website_url' => 'nullable|url',
            'contact_email' => 'nullable|email',
            'contact_phone' => 'nullable|string|max:20',
            'type' => 'required|in:official,gold,silver,bronze,partner,media',
            'display_order' => 'nullable|integer|min:0',
            'is_featured' => 'boolean',
            'is_active' => 'boolean',
        ]);

        if ($validator->fails()) {
            return back()->withErrors($validator)->withInput();
        }

        $data = $request->all();

        // Handle logo upload
        if ($request->hasFile('logo')) {
            $logoPath = $request->file('logo')->store('sponsors', 'public');
            $data['logo'] = basename($logoPath);
        }

        $data['is_featured'] = $request->has('is_featured');
        $data['is_active'] = $request->has('is_active');
        $data['display_order'] = $request->display_order ?: 0;

        Sponsor::create($data);

        return redirect()->route('admin.sponsors.index')
            ->with('success', 'Sponsor created successfully.');
    }

    public function show(Sponsor $sponsor)
    {
        $sponsor->load('conference');
        return view('admin.sponsors.show', compact('sponsor'));
    }

    public function edit(Sponsor $sponsor)
    {
        $conferences = Conference::active()->orderBy('title')->get();
        return view('admin.sponsors.edit', compact('sponsor', 'conferences'));
    }

    public function update(Request $request, Sponsor $sponsor)
    {
        $validator = Validator::make($request->all(), [
            'conference_id' => 'required|exists:conferences,id',
            'name' => 'required|string|max:255',
            'description' => 'nullable|string',
            'logo' => 'nullable|image|mimes:jpeg,png,jpg,gif,svg|max:2048',
            'website_url' => 'nullable|url',
            'contact_email' => 'nullable|email',
            'contact_phone' => 'nullable|string|max:20',
            'type' => 'required|in:official,gold,silver,bronze,partner,media',
            'display_order' => 'nullable|integer|min:0',
            'is_featured' => 'boolean',
            'is_active' => 'boolean',
        ]);

        if ($validator->fails()) {
            return back()->withErrors($validator)->withInput();
        }

        $data = $request->all();

        // Handle logo upload
        if ($request->hasFile('logo')) {
            // Delete old logo
            if ($sponsor->logo) {
                Storage::disk('public')->delete('sponsors/' . $sponsor->logo);
            }
            $logoPath = $request->file('logo')->store('sponsors', 'public');
            $data['logo'] = basename($logoPath);
        }

        $data['is_featured'] = $request->has('is_featured');
        $data['is_active'] = $request->has('is_active');
        $data['display_order'] = $request->display_order ?: 0;

        $sponsor->update($data);

        return redirect()->route('admin.sponsors.index')
            ->with('success', 'Sponsor updated successfully.');
    }

    public function destroy(Sponsor $sponsor)
    {
        // Delete logo
        if ($sponsor->logo) {
            Storage::disk('public')->delete('sponsors/' . $sponsor->logo);
        }

        $sponsor->delete();

        return redirect()->route('admin.sponsors.index')
            ->with('success', 'Sponsor deleted successfully.');
    }
}
