<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use App\Models\Registration;
use App\Models\Conference;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Validator;
use Maatwebsite\Excel\Facades\Excel;
use App\Exports\RegistrationsExport;

class RegistrationController extends Controller
{
    public function index(Request $request)
    {
        $query = Registration::with('conference');

        // Filter by conference
        if ($request->has('conference_id') && $request->conference_id) {
            $query->where('conference_id', $request->conference_id);
        }

        // Filter by payment status
        if ($request->has('payment_status') && $request->payment_status) {
            $query->where('payment_status', $request->payment_status);
        }

        // Filter by participant type
        if ($request->has('participant_type') && $request->participant_type) {
            $query->where('participant_type', $request->participant_type);
        }

        // Filter by confirmation status
        if ($request->has('is_confirmed') && $request->is_confirmed !== '') {
            $query->where('is_confirmed', $request->is_confirmed);
        }

        // Search
        if ($request->has('search') && $request->search) {
            $search = $request->search;
            $query->where(function($q) use ($search) {
                $q->where('first_name', 'like', "%{$search}%")
                  ->orWhere('last_name', 'like', "%{$search}%")
                  ->orWhere('email', 'like', "%{$search}%")
                  ->orWhere('organization', 'like', "%{$search}%")
                  ->orWhere('confirmation_code', 'like', "%{$search}%");
            });
        }

        $registrations = $query->orderBy('created_at', 'desc')->paginate(20);
        $conferences = Conference::active()->orderBy('title')->get();

        // Statistics
        $stats = [
            'total' => Registration::count(),
            'confirmed' => Registration::confirmed()->count(),
            'paid' => Registration::paid()->count(),
            'checked_in' => Registration::checkedIn()->count(),
            'local' => Registration::local()->count(),
            'international' => Registration::international()->count(),
        ];

        return view('admin.registrations.index', compact('registrations', 'conferences', 'stats'));
    }

    public function create()
    {
        $conferences = Conference::active()->orderBy('title')->get();
        return view('admin.registrations.create', compact('conferences'));
    }

    public function store(Request $request)
    {
        $validator = Validator::make($request->all(), [
            'conference_id' => 'required|exists:conferences,id',
            'first_name' => 'required|string|max:255',
            'last_name' => 'required|string|max:255',
            'email' => 'required|email|unique:registrations,email,NULL,id,conference_id,' . $request->conference_id,
            'phone' => 'nullable|string|max:20',
            'country' => 'required|string|max:100',
            'city' => 'nullable|string|max:100',
            'organization' => 'nullable|string|max:255',
            'position' => 'nullable|string|max:255',
            'participant_type' => 'required|in:local,international',
            'registration_type' => 'required|in:presenter,attendee',
            'dietary_requirements' => 'nullable|string|max:500',
            'special_needs' => 'nullable|string|max:500',
            'payment_status' => 'required|in:pending,paid,cancelled',
            'payment_method' => 'nullable|string|max:100',
            'payment_reference' => 'nullable|string|max:255',
            'registration_fee' => 'nullable|numeric|min:0',
            'currency' => 'nullable|string|max:3',
            'notes' => 'nullable|string',
            'is_confirmed' => 'boolean',
        ]);

        if ($validator->fails()) {
            return back()->withErrors($validator)->withInput();
        }

        $data = $request->all();
        $data['is_confirmed'] = $request->has('is_confirmed');

        $registration = Registration::create($data);

        // Generate confirmation code if not provided
        if (!$registration->confirmation_code) {
            $registration->generateConfirmationCode();
        }

        // Auto-confirm if marked as confirmed
        if ($data['is_confirmed'] && !$registration->confirmed_at) {
            $registration->confirm();
        }

        return redirect()->route('admin.registrations.index')
            ->with('success', 'Registration created successfully.');
    }

    public function show(Registration $registration)
    {
        $registration->load('conference');
        return view('admin.registrations.show', compact('registration'));
    }

    public function edit(Registration $registration)
    {
        $conferences = Conference::active()->orderBy('title')->get();
        return view('admin.registrations.edit', compact('registration', 'conferences'));
    }

    public function update(Request $request, Registration $registration)
    {
        $validator = Validator::make($request->all(), [
            'conference_id' => 'required|exists:conferences,id',
            'first_name' => 'required|string|max:255',
            'last_name' => 'required|string|max:255',
            'email' => 'required|email|unique:registrations,email,' . $registration->id . ',id,conference_id,' . $request->conference_id,
            'phone' => 'nullable|string|max:20',
            'country' => 'required|string|max:100',
            'city' => 'nullable|string|max:100',
            'organization' => 'nullable|string|max:255',
            'position' => 'nullable|string|max:255',
            'participant_type' => 'required|in:local,international',
            'registration_type' => 'required|in:presenter,attendee',
            'dietary_requirements' => 'nullable|string|max:500',
            'special_needs' => 'nullable|string|max:500',
            'payment_status' => 'required|in:pending,paid,cancelled',
            'payment_method' => 'nullable|string|max:100',
            'payment_reference' => 'nullable|string|max:255',
            'registration_fee' => 'nullable|numeric|min:0',
            'currency' => 'nullable|string|max:3',
            'notes' => 'nullable|string',
            'is_confirmed' => 'boolean',
        ]);

        if ($validator->fails()) {
            return back()->withErrors($validator)->withInput();
        }

        $data = $request->all();
        $wasConfirmed = $registration->is_confirmed;
        $data['is_confirmed'] = $request->has('is_confirmed');

        $registration->update($data);

        // Auto-confirm if marked as confirmed and wasn't confirmed before
        if ($data['is_confirmed'] && !$wasConfirmed) {
            $registration->confirm();
        }

        return redirect()->route('admin.registrations.index')
            ->with('success', 'Registration updated successfully.');
    }

    public function destroy(Registration $registration)
    {
        $registration->delete();

        return redirect()->route('admin.registrations.index')
            ->with('success', 'Registration deleted successfully.');
    }

    public function confirm(Registration $registration)
    {
        $registration->confirm();

        return back()->with('success', 'Registration confirmed successfully.');
    }

    public function checkIn(Registration $registration)
    {
        $registration->checkIn();

        return back()->with('success', 'Participant checked in successfully.');
    }

    public function exportExcel(Request $request)
    {
        $conferenceId = $request->get('conference_id');
        $filename = 'registrations_' . date('Y-m-d_H-i-s') . '.xlsx';

        return Excel::download(new RegistrationsExport($conferenceId), $filename);
    }

    public function exportCsv(Request $request)
    {
        $conferenceId = $request->get('conference_id');
        $filename = 'registrations_' . date('Y-m-d_H-i-s') . '.csv';

        return Excel::download(new RegistrationsExport($conferenceId), $filename, \Maatwebsite\Excel\Excel::CSV);
    }
}
