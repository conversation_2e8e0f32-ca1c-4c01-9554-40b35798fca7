<?php

use Illuminate\Http\Request;
use Illuminate\Support\Facades\Route;
use App\Http\Controllers\Api\AuthController;
use App\Http\Controllers\Api\ConferenceController;
use App\Http\Controllers\Api\SpeakerController;
use App\Http\Controllers\Api\RegistrationController;
use App\Http\Controllers\Api\NewsController;
use App\Http\Controllers\Api\SponsorController;
use App\Http\Controllers\Api\EventController;

/*
|--------------------------------------------------------------------------
| API Routes
|--------------------------------------------------------------------------
|
| Here is where you can register API routes for your application. These
| routes are loaded by the RouteServiceProvider and all of them will
| be assigned to the "api" middleware group. Make something great!
|
*/

// Public routes
Route::prefix('v1')->group(function () {
    // Authentication
    Route::post('/auth/login', [AuthController::class, 'login']);
    Route::post('/auth/register', [AuthController::class, 'register']);
    
    // Conferences
    Route::get('/conferences', [ConferenceController::class, 'index']);
    Route::get('/conferences/current', [ConferenceController::class, 'current']);
    Route::get('/conferences/{id}', [ConferenceController::class, 'show']);
    
    // Speakers
    Route::get('/speakers', [SpeakerController::class, 'index']);
    Route::get('/speakers/{id}', [SpeakerController::class, 'show']);
    Route::get('/speakers/committees/all', [SpeakerController::class, 'committees']);
    Route::get('/speakers/keynotes/all', [SpeakerController::class, 'keynotes']);
    
    // News
    Route::get('/news', [NewsController::class, 'index']);
    Route::get('/news/featured', [NewsController::class, 'featured']);
    Route::get('/news/recent', [NewsController::class, 'recent']);
    Route::get('/news/{slug}', [NewsController::class, 'show']);
    
    // Sponsors
    Route::get('/sponsors', [SponsorController::class, 'index']);
    Route::get('/sponsors/{id}', [SponsorController::class, 'show']);
    
    // Events
    Route::get('/events', [EventController::class, 'index']);
    Route::get('/events/{id}', [EventController::class, 'show']);
    Route::get('/events/schedule/all', [EventController::class, 'schedule']);
    
    // Registration
    Route::post('/registrations', [RegistrationController::class, 'store']);
    Route::get('/registrations/{confirmationCode}', [RegistrationController::class, 'show']);
    Route::post('/registrations/check-status', [RegistrationController::class, 'checkStatus']);
});

// Protected routes
Route::prefix('v1')->middleware('auth:api')->group(function () {
    // Authentication
    Route::post('/auth/logout', [AuthController::class, 'logout']);
    Route::post('/auth/refresh', [AuthController::class, 'refresh']);
    Route::get('/auth/me', [AuthController::class, 'me']);
    
    // Admin routes (require admin role)
    Route::middleware('admin')->group(function () {
        // Conference management
        Route::apiResource('admin/conferences', 'Admin\ConferenceController');
        
        // Speaker management
        Route::apiResource('admin/speakers', 'Admin\SpeakerController');
        
        // Registration management
        Route::apiResource('admin/registrations', 'Admin\RegistrationController');
        Route::post('admin/registrations/{id}/confirm', 'Admin\RegistrationController@confirm');
        Route::post('admin/registrations/{id}/check-in', 'Admin\RegistrationController@checkIn');
        Route::get('admin/registrations/export/excel', 'Admin\RegistrationController@exportExcel');
        Route::get('admin/registrations/export/csv', 'Admin\RegistrationController@exportCsv');
        
        // Sponsor management
        Route::apiResource('admin/sponsors', 'Admin\SponsorController');
        
        // News management
        Route::apiResource('admin/news', 'Admin\NewsController');
        Route::post('admin/news/{id}/publish', 'Admin\NewsController@publish');
        Route::post('admin/news/{id}/unpublish', 'Admin\NewsController@unpublish');
        
        // Event management
        Route::apiResource('admin/events', 'Admin\EventController');
        
        // Settings management
        Route::get('admin/settings', 'Admin\SettingController@index');
        Route::post('admin/settings', 'Admin\SettingController@update');
        
        // File uploads
        Route::post('admin/upload/image', 'Admin\FileController@uploadImage');
        Route::post('admin/upload/file', 'Admin\FileController@uploadFile');
        
        // Dashboard stats
        Route::get('admin/dashboard/stats', 'Admin\DashboardController@stats');
    });
});

// Fallback route for API
Route::fallback(function () {
    return response()->json([
        'success' => false,
        'message' => 'API endpoint not found'
    ], 404);
});
