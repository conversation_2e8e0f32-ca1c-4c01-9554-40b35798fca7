<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;

class Speaker extends Model
{
    use HasFactory, SoftDeletes;

    protected $fillable = [
        'conference_id',
        'name',
        'title',
        'position',
        'organization',
        'bio',
        'photo',
        'email',
        'phone',
        'linkedin_url',
        'twitter_url',
        'website_url',
        'country',
        'city',
        'type', // keynote, invited, committee_chair, committee_member
        'committee_type', // advisory, scientific, organizing
        'is_featured',
        'display_order',
        'is_active',
    ];

    protected $casts = [
        'is_featured' => 'boolean',
        'is_active' => 'boolean',
        'display_order' => 'integer',
    ];

    // Relationships
    public function conference()
    {
        return $this->belongsTo(Conference::class);
    }

    public function events()
    {
        return $this->belongsToMany(Event::class, 'event_speakers');
    }

    // Accessors
    public function getPhotoUrlAttribute()
    {
        return $this->photo ? asset('uploads/speakers/' . $this->photo) : asset('images/default-avatar.png');
    }

    public function getFullNameAttribute()
    {
        return $this->title ? $this->title . ' ' . $this->name : $this->name;
    }

    // Scopes
    public function scopeActive($query)
    {
        return $query->where('is_active', true);
    }

    public function scopeFeatured($query)
    {
        return $query->where('is_featured', true);
    }

    public function scopeByType($query, $type)
    {
        return $query->where('type', $type);
    }

    public function scopeByCommitteeType($query, $committeeType)
    {
        return $query->where('committee_type', $committeeType);
    }

    public function scopeOrdered($query)
    {
        return $query->orderBy('display_order')->orderBy('name');
    }

    // Methods
    public function isKeynote()
    {
        return $this->type === 'keynote';
    }

    public function isCommitteeMember()
    {
        return in_array($this->type, ['committee_chair', 'committee_member']);
    }
}
