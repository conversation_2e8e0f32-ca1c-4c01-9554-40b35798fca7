@extends('layouts.admin')

@section('title', 'Dashboard')

@section('content')
<div class="d-flex justify-content-between align-items-center mb-4">
    <h1 class="h3 mb-0">
        <i class="fas fa-tachometer-alt"></i> Dashboard
    </h1>
    <div class="text-muted">
        <i class="fas fa-calendar"></i> {{ now()->format('l, F j, Y') }}
    </div>
</div>

<!-- Statistics Cards -->
<div class="row mb-4">
    <div class="col-xl-3 col-md-6 mb-4">
        <div class="stats-card">
            <div class="stats-number">{{ $stats['total_conferences'] }}</div>
            <div class="stats-label">
                <i class="fas fa-calendar-alt"></i> Total Conferences
            </div>
        </div>
    </div>
    
    <div class="col-xl-3 col-md-6 mb-4">
        <div class="stats-card" style="background: linear-gradient(135deg, #38a169 0%, #48bb78 100%);">
            <div class="stats-number">{{ $stats['confirmed_registrations'] }}</div>
            <div class="stats-label">
                <i class="fas fa-user-check"></i> Confirmed Registrations
            </div>
        </div>
    </div>
    
    <div class="col-xl-3 col-md-6 mb-4">
        <div class="stats-card" style="background: linear-gradient(135deg, #d69e2e 0%, #ecc94b 100%);">
            <div class="stats-number">{{ $stats['active_speakers'] }}</div>
            <div class="stats-label">
                <i class="fas fa-users"></i> Active Speakers
            </div>
        </div>
    </div>
    
    <div class="col-xl-3 col-md-6 mb-4">
        <div class="stats-card" style="background: linear-gradient(135deg, #9f7aea 0%, #b794f6 100%);">
            <div class="stats-number">{{ $stats['active_sponsors'] }}</div>
            <div class="stats-label">
                <i class="fas fa-handshake"></i> Active Sponsors
            </div>
        </div>
    </div>
</div>

@if($currentConference)
<!-- Current Conference Info -->
<div class="row mb-4">
    <div class="col-12">
        <div class="card">
            <div class="card-header bg-primary text-white">
                <h5 class="card-title mb-0">
                    <i class="fas fa-star"></i> Current Conference
                </h5>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-md-8">
                        <h4>{{ $currentConference->title }}</h4>
                        <p class="text-muted mb-2">{{ $currentConference->subtitle }}</p>
                        <p><i class="fas fa-calendar"></i> {{ $currentConference->start_date->format('F j, Y') }} - {{ $currentConference->end_date->format('F j, Y') }}</p>
                        <p><i class="fas fa-map-marker-alt"></i> {{ $currentConference->location }}</p>
                    </div>
                    <div class="col-md-4">
                        <div class="row text-center">
                            <div class="col-6">
                                <div class="border rounded p-3">
                                    <h3 class="text-primary">{{ $registrationStats['total'] ?? 0 }}</h3>
                                    <small class="text-muted">Total Registrations</small>
                                </div>
                            </div>
                            <div class="col-6">
                                <div class="border rounded p-3">
                                    <h3 class="text-success">{{ $registrationStats['paid'] ?? 0 }}</h3>
                                    <small class="text-muted">Paid</small>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
@endif

<div class="row">
    <!-- Recent Registrations -->
    <div class="col-lg-8 mb-4">
        <div class="card">
            <div class="card-header d-flex justify-content-between align-items-center">
                <h5 class="card-title mb-0">
                    <i class="fas fa-user-plus"></i> Recent Registrations
                </h5>
                <a href="{{ route('admin.registrations.index') }}" class="btn btn-sm btn-outline-primary">
                    View All
                </a>
            </div>
            <div class="card-body">
                @if($recentRegistrations->count() > 0)
                    <div class="table-responsive">
                        <table class="table table-hover">
                            <thead>
                                <tr>
                                    <th>Name</th>
                                    <th>Email</th>
                                    <th>Type</th>
                                    <th>Status</th>
                                    <th>Date</th>
                                </tr>
                            </thead>
                            <tbody>
                                @foreach($recentRegistrations as $registration)
                                <tr>
                                    <td>
                                        <strong>{{ $registration->full_name }}</strong>
                                        @if($registration->organization)
                                            <br><small class="text-muted">{{ $registration->organization }}</small>
                                        @endif
                                    </td>
                                    <td>{{ $registration->email }}</td>
                                    <td>
                                        <span class="badge bg-info">
                                            {{ ucfirst($registration->participant_type) }}
                                        </span>
                                    </td>
                                    <td>
                                        @if($registration->payment_status === 'paid')
                                            <span class="badge bg-success">Paid</span>
                                        @elseif($registration->payment_status === 'pending')
                                            <span class="badge bg-warning">Pending</span>
                                        @else
                                            <span class="badge bg-danger">Cancelled</span>
                                        @endif
                                    </td>
                                    <td>
                                        <small>{{ $registration->created_at->format('M j, Y') }}</small>
                                    </td>
                                </tr>
                                @endforeach
                            </tbody>
                        </table>
                    </div>
                @else
                    <div class="text-center py-4">
                        <i class="fas fa-inbox fa-3x text-muted mb-3"></i>
                        <p class="text-muted">No registrations yet</p>
                    </div>
                @endif
            </div>
        </div>
    </div>

    <!-- Quick Stats -->
    <div class="col-lg-4 mb-4">
        <div class="card">
            <div class="card-header">
                <h5 class="card-title mb-0">
                    <i class="fas fa-chart-pie"></i> Quick Stats
                </h5>
            </div>
            <div class="card-body">
                <div class="mb-3">
                    <div class="d-flex justify-content-between align-items-center mb-1">
                        <span>Published News</span>
                        <span class="badge bg-primary">{{ $stats['published_news'] }}</span>
                    </div>
                    <div class="progress" style="height: 5px;">
                        <div class="progress-bar" style="width: {{ $stats['total_news'] > 0 ? ($stats['published_news'] / $stats['total_news']) * 100 : 0 }}%"></div>
                    </div>
                </div>

                <div class="mb-3">
                    <div class="d-flex justify-content-between align-items-center mb-1">
                        <span>Active Events</span>
                        <span class="badge bg-success">{{ $stats['active_events'] }}</span>
                    </div>
                    <div class="progress" style="height: 5px;">
                        <div class="progress-bar bg-success" style="width: {{ $stats['total_events'] > 0 ? ($stats['active_events'] / $stats['total_events']) * 100 : 0 }}%"></div>
                    </div>
                </div>

                <div class="mb-3">
                    <div class="d-flex justify-content-between align-items-center mb-1">
                        <span>Featured Speakers</span>
                        <span class="badge bg-warning">{{ $stats['active_speakers'] }}</span>
                    </div>
                </div>

                <div class="mb-3">
                    <div class="d-flex justify-content-between align-items-center mb-1">
                        <span>Active Sponsors</span>
                        <span class="badge bg-info">{{ $stats['active_sponsors'] }}</span>
                    </div>
                </div>
            </div>
        </div>

        <!-- Recent News -->
        <div class="card mt-4">
            <div class="card-header d-flex justify-content-between align-items-center">
                <h5 class="card-title mb-0">
                    <i class="fas fa-newspaper"></i> Recent News
                </h5>
                <a href="{{ route('admin.news.index') }}" class="btn btn-sm btn-outline-primary">
                    View All
                </a>
            </div>
            <div class="card-body">
                @if($recentNews->count() > 0)
                    @foreach($recentNews as $news)
                    <div class="d-flex mb-3 {{ !$loop->last ? 'border-bottom pb-3' : '' }}">
                        <div class="flex-grow-1">
                            <h6 class="mb-1">{{ Str::limit($news->title, 50) }}</h6>
                            <small class="text-muted">
                                <i class="fas fa-calendar"></i> {{ $news->created_at->format('M j, Y') }}
                                @if($news->is_published)
                                    <span class="badge bg-success ms-1">Published</span>
                                @else
                                    <span class="badge bg-secondary ms-1">Draft</span>
                                @endif
                            </small>
                        </div>
                    </div>
                    @endforeach
                @else
                    <div class="text-center py-3">
                        <i class="fas fa-newspaper fa-2x text-muted mb-2"></i>
                        <p class="text-muted mb-0">No news articles yet</p>
                    </div>
                @endif
            </div>
        </div>
    </div>
</div>

<!-- Upcoming Events -->
@if($upcomingEvents->count() > 0)
<div class="row">
    <div class="col-12">
        <div class="card">
            <div class="card-header d-flex justify-content-between align-items-center">
                <h5 class="card-title mb-0">
                    <i class="fas fa-calendar-check"></i> Upcoming Events
                </h5>
                <a href="{{ route('admin.events.index') }}" class="btn btn-sm btn-outline-primary">
                    View All
                </a>
            </div>
            <div class="card-body">
                <div class="row">
                    @foreach($upcomingEvents as $event)
                    <div class="col-md-6 col-lg-4 mb-3">
                        <div class="border rounded p-3">
                            <h6 class="mb-2">{{ $event->title }}</h6>
                            <p class="text-muted mb-1">
                                <i class="fas fa-calendar"></i> {{ $event->start_time->format('M j, Y') }}
                            </p>
                            <p class="text-muted mb-1">
                                <i class="fas fa-clock"></i> {{ $event->start_time->format('H:i') }} - {{ $event->end_time->format('H:i') }}
                            </p>
                            @if($event->location)
                            <p class="text-muted mb-0">
                                <i class="fas fa-map-marker-alt"></i> {{ $event->location }}
                            </p>
                            @endif
                        </div>
                    </div>
                    @endforeach
                </div>
            </div>
        </div>
    </div>
</div>
@endif
@endsection

@push('scripts')
<script>
    // Auto-refresh dashboard every 5 minutes
    setTimeout(function() {
        location.reload();
    }, 300000);
</script>
@endpush
