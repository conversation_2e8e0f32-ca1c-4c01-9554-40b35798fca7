<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Storage;
use Illuminate\Support\Facades\Validator;
use Intervention\Image\Facades\Image;

class FileController extends Controller
{
    public function uploadImage(Request $request)
    {
        $validator = Validator::make($request->all(), [
            'image' => 'required|image|mimes:jpeg,png,jpg,gif,svg|max:5120',
            'folder' => 'nullable|string|in:conferences,speakers,sponsors,news,events',
            'resize' => 'nullable|boolean',
            'width' => 'nullable|integer|min:50|max:2000',
            'height' => 'nullable|integer|min:50|max:2000',
        ]);

        if ($validator->fails()) {
            return response()->json([
                'success' => false,
                'message' => 'Validation error',
                'errors' => $validator->errors()
            ], 422);
        }

        try {
            $folder = $request->folder ?: 'uploads';
            $file = $request->file('image');
            
            // Generate unique filename
            $filename = time() . '_' . uniqid() . '.' . $file->getClientOriginalExtension();
            
            // Resize image if requested
            if ($request->resize) {
                $image = Image::make($file);
                
                $width = $request->width ?: 800;
                $height = $request->height ?: null;
                
                if ($height) {
                    $image->resize($width, $height, function ($constraint) {
                        $constraint->aspectRatio();
                        $constraint->upsize();
                    });
                } else {
                    $image->resize($width, null, function ($constraint) {
                        $constraint->aspectRatio();
                        $constraint->upsize();
                    });
                }
                
                // Save resized image
                $path = storage_path('app/public/' . $folder . '/' . $filename);
                $image->save($path);
            } else {
                // Save original image
                $file->storeAs($folder, $filename, 'public');
            }

            $url = asset('storage/' . $folder . '/' . $filename);

            return response()->json([
                'success' => true,
                'message' => 'Image uploaded successfully',
                'data' => [
                    'filename' => $filename,
                    'url' => $url,
                    'path' => $folder . '/' . $filename,
                ]
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to upload image: ' . $e->getMessage()
            ], 500);
        }
    }

    public function uploadFile(Request $request)
    {
        $validator = Validator::make($request->all(), [
            'file' => 'required|file|max:10240', // 10MB max
            'folder' => 'nullable|string|in:documents,media,uploads',
        ]);

        if ($validator->fails()) {
            return response()->json([
                'success' => false,
                'message' => 'Validation error',
                'errors' => $validator->errors()
            ], 422);
        }

        try {
            $folder = $request->folder ?: 'uploads';
            $file = $request->file('file');
            
            // Generate unique filename
            $originalName = pathinfo($file->getClientOriginalName(), PATHINFO_FILENAME);
            $extension = $file->getClientOriginalExtension();
            $filename = time() . '_' . uniqid() . '_' . $originalName . '.' . $extension;
            
            // Store file
            $file->storeAs($folder, $filename, 'public');
            
            $url = asset('storage/' . $folder . '/' . $filename);

            return response()->json([
                'success' => true,
                'message' => 'File uploaded successfully',
                'data' => [
                    'filename' => $filename,
                    'original_name' => $file->getClientOriginalName(),
                    'url' => $url,
                    'path' => $folder . '/' . $filename,
                    'size' => $file->getSize(),
                    'mime_type' => $file->getMimeType(),
                ]
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to upload file: ' . $e->getMessage()
            ], 500);
        }
    }

    public function deleteFile($filename)
    {
        try {
            // Find file in common folders
            $folders = ['conferences', 'speakers', 'sponsors', 'news', 'events', 'uploads', 'documents', 'media'];
            $deleted = false;

            foreach ($folders as $folder) {
                $path = $folder . '/' . $filename;
                if (Storage::disk('public')->exists($path)) {
                    Storage::disk('public')->delete($path);
                    $deleted = true;
                    break;
                }
            }

            if (!$deleted) {
                return response()->json([
                    'success' => false,
                    'message' => 'File not found'
                ], 404);
            }

            return response()->json([
                'success' => true,
                'message' => 'File deleted successfully'
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to delete file: ' . $e->getMessage()
            ], 500);
        }
    }
}
