@extends('layouts.admin')

@section('title', 'Conferences')

@section('content')
<div class="d-flex justify-content-between align-items-center mb-4">
    <h1 class="h3 mb-0">
        <i class="fas fa-calendar-alt"></i> Conferences
    </h1>
    <a href="{{ route('admin.conferences.create') }}" class="btn btn-primary">
        <i class="fas fa-plus"></i> Add New Conference
    </a>
</div>

<div class="card">
    <div class="card-body">
        @if($conferences->count() > 0)
            <div class="table-responsive">
                <table class="table table-hover">
                    <thead>
                        <tr>
                            <th>Title</th>
                            <th>Date</th>
                            <th>Location</th>
                            <th>Statistics</th>
                            <th>Status</th>
                            <th>Actions</th>
                        </tr>
                    </thead>
                    <tbody>
                        @foreach($conferences as $conference)
                        <tr>
                            <td>
                                <div>
                                    <strong>{{ $conference->title }}</strong>
                                    @if($conference->subtitle)
                                        <br><small class="text-muted">{{ $conference->subtitle }}</small>
                                    @endif
                                </div>
                            </td>
                            <td>
                                <div>
                                    <strong>{{ $conference->start_date->format('M j, Y') }}</strong>
                                    <br><small class="text-muted">to {{ $conference->end_date->format('M j, Y') }}</small>
                                </div>
                            </td>
                            <td>
                                <div>
                                    {{ $conference->location }}
                                    @if($conference->venue)
                                        <br><small class="text-muted">{{ $conference->venue }}</small>
                                    @endif
                                </div>
                            </td>
                            <td>
                                <div class="row text-center">
                                    <div class="col-6">
                                        <small class="text-muted">Speakers</small>
                                        <br><strong>{{ $conference->speakers_count }}</strong>
                                    </div>
                                    <div class="col-6">
                                        <small class="text-muted">Registrations</small>
                                        <br><strong>{{ $conference->registrations_count }}</strong>
                                    </div>
                                </div>
                                <div class="row text-center mt-2">
                                    <div class="col-6">
                                        <small class="text-muted">Sponsors</small>
                                        <br><strong>{{ $conference->sponsors_count }}</strong>
                                    </div>
                                    <div class="col-6">
                                        <small class="text-muted">Events</small>
                                        <br><strong>{{ $conference->events_count }}</strong>
                                    </div>
                                </div>
                            </td>
                            <td>
                                @if($conference->is_active)
                                    <span class="badge bg-success">Active</span>
                                @else
                                    <span class="badge bg-secondary">Inactive</span>
                                @endif
                                
                                @if($conference->start_date->isFuture())
                                    <br><span class="badge bg-info mt-1">Upcoming</span>
                                @elseif($conference->end_date->isPast())
                                    <br><span class="badge bg-warning mt-1">Past</span>
                                @else
                                    <br><span class="badge bg-primary mt-1">Current</span>
                                @endif
                            </td>
                            <td>
                                <div class="btn-group" role="group">
                                    <a href="{{ route('admin.conferences.show', $conference) }}" 
                                       class="btn btn-sm btn-outline-info" title="View">
                                        <i class="fas fa-eye"></i>
                                    </a>
                                    <a href="{{ route('admin.conferences.edit', $conference) }}" 
                                       class="btn btn-sm btn-outline-primary" title="Edit">
                                        <i class="fas fa-edit"></i>
                                    </a>
                                    <form action="{{ route('admin.conferences.destroy', $conference) }}" 
                                          method="POST" 
                                          class="d-inline"
                                          onsubmit="return confirm('Are you sure you want to delete this conference?')">
                                        @csrf
                                        @method('DELETE')
                                        <button type="submit" class="btn btn-sm btn-outline-danger" title="Delete">
                                            <i class="fas fa-trash"></i>
                                        </button>
                                    </form>
                                </div>
                            </td>
                        </tr>
                        @endforeach
                    </tbody>
                </table>
            </div>

            <!-- Pagination -->
            <div class="d-flex justify-content-center mt-4">
                {{ $conferences->links() }}
            </div>
        @else
            <div class="text-center py-5">
                <i class="fas fa-calendar-alt fa-4x text-muted mb-3"></i>
                <h4 class="text-muted">No Conferences Found</h4>
                <p class="text-muted">Start by creating your first conference.</p>
                <a href="{{ route('admin.conferences.create') }}" class="btn btn-primary">
                    <i class="fas fa-plus"></i> Create Conference
                </a>
            </div>
        @endif
    </div>
</div>
@endsection
