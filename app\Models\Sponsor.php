<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;

class Sponsor extends Model
{
    use HasFactory, SoftDeletes;

    protected $fillable = [
        'conference_id',
        'name',
        'description',
        'logo',
        'website_url',
        'contact_email',
        'contact_phone',
        'type', // official, gold, silver, bronze, partner, media
        'display_order',
        'is_featured',
        'is_active',
    ];

    protected $casts = [
        'is_featured' => 'boolean',
        'is_active' => 'boolean',
        'display_order' => 'integer',
    ];

    // Relationships
    public function conference()
    {
        return $this->belongsTo(Conference::class);
    }

    // Accessors
    public function getLogoUrlAttribute()
    {
        return $this->logo ? asset('uploads/sponsors/' . $this->logo) : null;
    }

    public function getTypeDisplayAttribute()
    {
        $types = [
            'official' => 'Official Sponsor',
            'gold' => 'Gold Sponsor',
            'silver' => 'Silver Sponsor',
            'bronze' => 'Bronze Sponsor',
            'partner' => 'Partner',
            'media' => 'Media Partner',
        ];

        return $types[$this->type] ?? ucfirst($this->type);
    }

    // Scopes
    public function scopeActive($query)
    {
        return $query->where('is_active', true);
    }

    public function scopeFeatured($query)
    {
        return $query->where('is_featured', true);
    }

    public function scopeByType($query, $type)
    {
        return $query->where('type', $type);
    }

    public function scopeOrdered($query)
    {
        return $query->orderBy('display_order')->orderBy('name');
    }

    // Methods
    public function isOfficial()
    {
        return $this->type === 'official';
    }

    public function isPremium()
    {
        return in_array($this->type, ['official', 'gold']);
    }
}
