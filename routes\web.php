<?php

use Illuminate\Support\Facades\Route;
use App\Http\Controllers\Admin\AuthController;
use App\Http\Controllers\Admin\DashboardController;
use App\Http\Controllers\Admin\ConferenceController;
use App\Http\Controllers\Admin\SpeakerController;
use App\Http\Controllers\Admin\RegistrationController;
use App\Http\Controllers\Admin\SponsorController;
use App\Http\Controllers\Admin\NewsController;
use App\Http\Controllers\Admin\EventController;
use App\Http\Controllers\Admin\SettingController;
use App\Http\Controllers\Admin\FileController;

/*
|--------------------------------------------------------------------------
| Web Routes
|--------------------------------------------------------------------------
|
| Here is where you can register web routes for your application. These
| routes are loaded by the RouteServiceProvider and all of them will
| be assigned to the "web" middleware group. Make something great!
|
*/

// Redirect root to admin
Route::get('/', function () {
    return redirect('/admin');
});

// Admin Authentication Routes
Route::prefix('admin')->group(function () {
    // Guest routes (not authenticated)
    Route::middleware('guest')->group(function () {
        Route::get('/login', [AuthController::class, 'showLoginForm'])->name('admin.login');
        Route::post('/login', [AuthController::class, 'login'])->name('admin.login.post');
    });
    
    // Authenticated routes
    Route::middleware(['auth', 'admin'])->group(function () {
        Route::post('/logout', [AuthController::class, 'logout'])->name('admin.logout');
        
        // Dashboard
        Route::get('/', [DashboardController::class, 'index'])->name('admin.dashboard');
        Route::get('/dashboard', [DashboardController::class, 'index'])->name('admin.dashboard.index');
        
        // Conference Management
        Route::resource('conferences', ConferenceController::class)->names([
            'index' => 'admin.conferences.index',
            'create' => 'admin.conferences.create',
            'store' => 'admin.conferences.store',
            'show' => 'admin.conferences.show',
            'edit' => 'admin.conferences.edit',
            'update' => 'admin.conferences.update',
            'destroy' => 'admin.conferences.destroy',
        ]);
        
        // Speaker Management
        Route::resource('speakers', SpeakerController::class)->names([
            'index' => 'admin.speakers.index',
            'create' => 'admin.speakers.create',
            'store' => 'admin.speakers.store',
            'show' => 'admin.speakers.show',
            'edit' => 'admin.speakers.edit',
            'update' => 'admin.speakers.update',
            'destroy' => 'admin.speakers.destroy',
        ]);
        
        // Registration Management
        Route::resource('registrations', RegistrationController::class)->names([
            'index' => 'admin.registrations.index',
            'create' => 'admin.registrations.create',
            'store' => 'admin.registrations.store',
            'show' => 'admin.registrations.show',
            'edit' => 'admin.registrations.edit',
            'update' => 'admin.registrations.update',
            'destroy' => 'admin.registrations.destroy',
        ]);
        Route::post('registrations/{registration}/confirm', [RegistrationController::class, 'confirm'])->name('admin.registrations.confirm');
        Route::post('registrations/{registration}/check-in', [RegistrationController::class, 'checkIn'])->name('admin.registrations.checkin');
        Route::get('registrations/export/excel', [RegistrationController::class, 'exportExcel'])->name('admin.registrations.export.excel');
        Route::get('registrations/export/csv', [RegistrationController::class, 'exportCsv'])->name('admin.registrations.export.csv');
        
        // Sponsor Management
        Route::resource('sponsors', SponsorController::class)->names([
            'index' => 'admin.sponsors.index',
            'create' => 'admin.sponsors.create',
            'store' => 'admin.sponsors.store',
            'show' => 'admin.sponsors.show',
            'edit' => 'admin.sponsors.edit',
            'update' => 'admin.sponsors.update',
            'destroy' => 'admin.sponsors.destroy',
        ]);
        
        // News Management
        Route::resource('news', NewsController::class)->names([
            'index' => 'admin.news.index',
            'create' => 'admin.news.create',
            'store' => 'admin.news.store',
            'show' => 'admin.news.show',
            'edit' => 'admin.news.edit',
            'update' => 'admin.news.update',
            'destroy' => 'admin.news.destroy',
        ]);
        Route::post('news/{news}/publish', [NewsController::class, 'publish'])->name('admin.news.publish');
        Route::post('news/{news}/unpublish', [NewsController::class, 'unpublish'])->name('admin.news.unpublish');
        
        // Event Management
        Route::resource('events', EventController::class)->names([
            'index' => 'admin.events.index',
            'create' => 'admin.events.create',
            'store' => 'admin.events.store',
            'show' => 'admin.events.show',
            'edit' => 'admin.events.edit',
            'update' => 'admin.events.update',
            'destroy' => 'admin.events.destroy',
        ]);
        
        // Settings
        Route::get('settings', [SettingController::class, 'index'])->name('admin.settings.index');
        Route::post('settings', [SettingController::class, 'update'])->name('admin.settings.update');
        
        // File Uploads
        Route::post('upload/image', [FileController::class, 'uploadImage'])->name('admin.upload.image');
        Route::post('upload/file', [FileController::class, 'uploadFile'])->name('admin.upload.file');
        Route::delete('upload/{filename}', [FileController::class, 'deleteFile'])->name('admin.upload.delete');
    });
});
